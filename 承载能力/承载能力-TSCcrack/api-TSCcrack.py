#!/usr/bin/env python
# coding: utf-8

import os
import random
import pickle
import warnings
import numpy as np
import pandas as pd
from xgboost import XGBRegressor
from scipy.interpolate import griddata

seed = 2024
random.seed(seed)
np.random.seed(seed)

# 忽略 DeprecationWarning 警告
warnings.filterwarnings("ignore", category=DeprecationWarning)

param = {
'EnterInternalPressure': 17,  #运行压力
    'PipeYieldStrength':528, #管道屈服强度
    'PipeTensileStrength':636,#管道抗拉强度
'OuterDiameter':1422, #管道外直径D
'WallThickness':32.1, #管道壁厚t
'DefectLength':25, #缺陷长度Lc
'DefectDepth':4, # 缺陷深度dc
'CTOD':0.16,# thataA
'CalOut':'TSCcrack' # TSCcrack
}

def validate_parameters(*args):
    for inx, param in enumerate(args):
        if inx == 8 and param not in ['TSCcrack']:
            return 201, f"'{desc[inx]}' must be TSCcrack"
        else:
            return 200, 'success'
            
        
        if not isinstance(param, (int, float)):
            return 202, f"'{desc[inx]}' must be a number"
        
        if param <= 0:
            return 203, f"'{desc[inx]}' must be non-negative"
        
    return 200, 'success'

def cal_tsc_crack(c, a, t, fp, thetaA, thetay, thetaU):
    """
    c = 1  #裂纹长度
    a = 1  #裂纹深度
    t = 1  #管道壁厚
    thetay = 1  #屈服强度
    thetaU = 1  #抗拉强度
    
    """
    c = float(c)
    a = float(a)
    t = float(t)
    thetaA = float(thetaA)
    thetay = float(thetay)
    thetaU = float(thetaU)
    # a列
    a1 = 2.084E+00
    a2 = 2.812E-01
    a3 = -4.950E-01
    a4 = 7.373E-01
    a5 = -5.005E+00
    a6 = 1.186E+00
    a7 = 1.644E+00
    a8 = 7.374E-01
    a9 = -9.829E-01
    a10 = 8.655E-02
    a11 = -1.029E-01
    a12 = -1.500E-01
    a13 = 1.025E+00
    a14 = 5.557E+00

    # b列
    b1 = -5.005E-02
    b2 = -5.139E-03
    b3 = 4.485E-01
    b4 = 1.417E+00
    b5 = 2.217E+00
    b6 = 1.029E+00
    b7 = -2.598E+00
    b8 = -2.679E+00
    b9 = 1.694E+00

    # c列
    c1 = 1.409E+00
    c2 = 2.345E-01
    c3 = 1.125E+00
    c4 = 4.181E+00
    c5 = 1.201E+00
    c6 = -5.384E+00
    c7 = 2.406E+00
    c8 = -2.154E-01
    c9 = -5.237E-03
    c10 = 9.889E+00
    c11 = 3.547E-01
    c12 = -7.513E-01

    # d列
    d1 = 2.209E-02
    d2 = 1.156E+00
    d3 = 1.601E+00
    d4 = 8.964E-01
    d5 = 1.383E+00
    d6 = 1.333E+00
    d7 = 9.313E-02
    d8 = -2.240E+00
    d9 = 8.559E-00
    d10 = -3.719E+00

    #fp = 0.7
    #TSCp = 0.9
    
    #thetaA = 0.01
    e = 2.71 #自然常数
    h = 1
    phi = 1

    ########### 以上是常量

    beta, eta, psi, xi = 2*c/t, a/t, h/t, thetay/thetaU

    # Define the equations
    A = (a1 * (e ** (a2 / beta)) * (e ** (a3 * eta * beta * (e ** (a4 / beta)))) *
         (1 + a5 * (psi ** a6) + a7 * psi * (eta * beta) ** a8) *
         (1 + a9 * (xi ** a10) * (phi ** a11) + a12 * (psi ** a13) * (xi ** a14)))

    B = (beta ** b1 * (eta ** (b2 * (beta ** b3) / eta)) *
         (b4 * (phi ** b5) * ((b6 * (phi ** b7)) ** xi) + b8 * (psi ** b9)))

    C = (e ** (c1 / beta) * (e ** ((c2 * beta) / ((1 + c3 * beta) * eta))) *
         (1 + c4 * (psi ** c5) + c6 * psi * (eta ** -eta) + c7 * psi * (eta ** -beta)) *
         (c8 + c9 * (phi ** 10) + c11 * (xi ** c12) * phi))

    D = (d1 * (beta ** d2) * (eta ** ((d3 * beta) / (1 + d4 * beta))) *
         (1 + d5 * (psi ** d6)) * (1 + d7 * (xi ** d8) + d9 * (phi ** 10)))

    TSCp = A * (C*thetaA) ** (B*(thetaA**D))
    
    if 0.6<=fp<=0.8:
        return TSCp
    else:
        TSC0 = 1.5 * TSCp
        return TSC0 + (5 * fp/3) * (TSCp - TSC0)
    return TSCp

def main(param):
    # 获取输入值
    D = param.get('OuterDiameter') or 1422
    t = param.get('WallThickness') or 32.1
    P = param.get('EnterInternalPressure')  or 17
    thetaA = param.get('CTOD') or 1.6
    thetay = param.get('PipeYieldStrength') or 528
    thetaU = param.get('PipeTensileStrength') or 636
    c = param.get('DefectLength') or 25
    a = param.get('DefectDepth') or 4
    cal = param.get('CalOut') or 'TSCcrack'
    myargs = [D, t, P, thetay, thetaU, thetaA, c, a, cal]

    desc = ['OuterDiameter', 'WallThickness', 'EnterInternalPressure', 
            'PipeYieldStrength', 'PipeTensileStrength','CTOD','DefectLength','DefectDepth','CalOut']


    results = {'code':200,
              'data':{},
              'param':param,
              'message':'success'}
    code, mes = validate_parameters(*myargs)

    if code == 200:
        fp = P / ((thetay * 2 * t) / D)
        TSC = cal_tsc_crack(c, a, t, fp, thetaA, thetay, thetaU)
        TSCcrack = round(TSC*100,2)
        ryt = round(thetay/thetaU,2)
        print('11',TSCcrack)
        results['data'] = {cal:TSCcrack,'PF':fp,'Ryt':ryt}
    else:
        results['code'] = code
        results['message'] = mes

    return results

if '__name__' == '__mian__':
    results = main(param)