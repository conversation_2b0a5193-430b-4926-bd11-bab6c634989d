import os
import PySimpleGUI as sg
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font, PatternFill,Border, Side

def cal_tsc(c, a, t, fp, thetaA, thetay, thetaU):
    """
    c = 1  #裂纹长度
    a = 1  #裂纹深度
    t = 1  #管道壁厚
    thetay = 1  #屈服强度
    thetaU = 1  #抗拉强度
    
    """
    c = float(c)
    a = float(a)
    t = float(t)
    thetaA = float(thetaA)
    thetay = float(thetay)
    thetaU = float(thetaU)
    # a列
    a1 = 2.084E+00
    a2 = 2.812E-01
    a3 = -4.950E-01
    a4 = 7.373E-01
    a5 = -5.005E+00
    a6 = 1.186E+00
    a7 = 1.644E+00
    a8 = 7.374E-01
    a9 = -9.829E-01
    a10 = 8.655E-02
    a11 = -1.029E-01
    a12 = -1.500E-01
    a13 = 1.025E+00
    a14 = 5.557E+00

    # b列
    b1 = -5.005E-02
    b2 = -5.139E-03
    b3 = 4.485E-01
    b4 = 1.417E+00
    b5 = 2.217E+00
    b6 = 1.029E+00
    b7 = -2.598E+00
    b8 = -2.679E+00
    b9 = 1.694E+00

    # c列
    c1 = 1.409E+00
    c2 = 2.345E-01
    c3 = 1.125E+00
    c4 = 4.181E+00
    c5 = 1.201E+00
    c6 = -5.384E+00
    c7 = 2.406E+00
    c8 = -2.154E-01
    c9 = -5.237E-03
    c10 = 9.889E+00
    c11 = 3.547E-01
    c12 = -7.513E-01

    # d列
    d1 = 2.209E-02
    d2 = 1.156E+00
    d3 = 1.601E+00
    d4 = 8.964E-01
    d5 = 1.383E+00
    d6 = 1.333E+00
    d7 = 9.313E-02
    d8 = -2.240E+00
    d9 = 8.559E-00
    d10 = -3.719E+00

    #fp = 0.7 
    #TSCp = 0.9
    
    #thetaA = 0.01
    e = 2.71 #自然常数
    h = 0
    phi = 1

    ########### 以上是常量

#     c = 1  #裂纹长度
#     a = 1  #裂纹深度
#     t = 1  #管道壁厚
#     thetay = 1  #屈服强度
#     thetaU = 1  #抗拉强度

    beta, eta, psi, xi = 2*c/t, a/t, h/t, thetay/thetaU

    # Define the equations
    A = (a1 * (e ** (a2 / beta)) * (e ** (a3 * eta * beta * (e ** (a4 / beta)))) *
         (1 + a5 * (psi ** a6) + a7 * psi * (eta * beta) ** a8) *
         (1 + a9 * (xi ** a10) * (phi ** a11) + a12 * (psi ** a13) * (xi ** a14)))

    B = (beta ** b1 * (eta ** (b2 * (beta ** b3) / eta)) *
         (b4 * (phi ** b5) * ((b6 * (phi ** b7)) ** xi) + b8 * (psi ** b9)))

    C = (e ** (c1 / beta) * (e ** ((c2 * beta) / ((1 + c3 * beta) * eta))) *
         (1 + c4 * (psi ** c5) + c6 * psi * (eta ** -eta) + c7 * psi * (eta ** -beta)) *
         (c8 + c9 * (phi ** 10) + c11 * (xi ** c12) * phi))

    D = (d1 * (beta ** d2) * (eta ** ((d3 * beta) / (1 + d4 * beta))) *
         (1 + d5 * (psi ** d6)) * (1 + d7 * (xi ** d8) + d9 * (phi ** 10)))

    TSCp = A * (C*thetaA) ** (B*(thetaA**D))
    
    if 0.6<=fp<=0.8:
        return TSCp
    else:
        TSC0 = 1.5 * TSCp
        return TSC0 + (5 * fp/3) * (TSCp - TSC0)
        

    # Print the results
#     print("A =", A)
#     print("B =", B)
#     print("C =", C)
#     print("D =", D)
    return TSCp

# 定义布局
layout = [
    [sg.Text("含平面型缺陷管体拉伸应变承载能力评价", 
             #font=("Arial", 14), 
             justification='center', size=(90, 1), background_color='#2f89fc',font=('Arial', 15, 'bold'),##00d1ff
             text_color="white", pad=(0, 0))],
    
    [sg.Frame(
        "填写信息",
        [
            [sg.Text("填表人：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Input(key="-NAME-", expand_x=True,pad=(10, 10))],
            [sg.Text("填写日期：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Input(key="-DATE-", default_text="2024/7/26", expand_x=True,pad=(10, 10))],
            [sg.Text("备注：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Multiline(key="-REMARK-", size=(10, 5), expand_x=True,pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,  # 让框架宽度占满整个窗口
        pad=(10, 10)
    )],
    
    [sg.Frame(
        "管道几何结构",
        [
            [sg.Text("管道外径（mm）", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-DIAMETER-", default_text="1422", pad=(10, 10))],
            [sg.Text("管道壁厚（mm）", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-THICKNESS-", default_text="32.1", pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,  # 让框架宽度占满整个窗口
        pad=(10, 10)
    ),
    sg.Frame(
        "运行压力",
        [
            [sg.Text("运行压力（MPa）", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-PRESSURE-", default_text="10", pad=(10, 10))],
            [sg.Text("压力因子 fp", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-PRESSURE_FACTOR-", pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,  # 让框架宽度占满整个窗口
        pad=(10, 10)
    )],
    
    [sg.Frame(
        "管道力学性能",
        [
            [sg.Text("屈服强度（MPa）", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-YIELD_STRENGTH-", default_text="528", pad=(10, 10))],
            [sg.Text("抗拉强度（MPa）", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-TENSILE_STRENGTH-", default_text="636", pad=(10, 10))],
            [sg.Text("屈强比 Y/T", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-Y_T_RATIO-", default_text="0.83", pad=(10, 10))],
            [sg.Text("CTOD值-δA(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-CTOD-", default_text="2.84",pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,  # 让框架宽度占满整个窗口
        pad=(10, 10)
    ),
    sg.Frame(
        "缺陷几何",
        [
            [sg.Text("裂纹深度 a（mm）", size=(20, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-DEFECT_DEPTH-", default_text="3.21", pad=(10, 10))],
            [sg.Text("裂纹长度 c（mm）", size=(20, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-DEFECT_LENGTH-", default_text="72", pad=(10, 10))]
            #[sg.Text("缺陷横向长度 Wc（mm）", size=(20, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-DEFECT_WIDTH-", default_text="72", pad=(10, 10))],
            
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,  # 让框架宽度占满整个窗口
        pad=(10, 10)
    )],
    
    [sg.Text("拉伸应变能力TSCerack(%)", size=(20, 1), text_color="black", background_color="white", border_width=0,pad=(10, 10)), sg.Input(key="-TSC-", size=(20, 1), pad=(10, 10))],
    
    [sg.Button("""计算TSCerack (%)""", pad=(10, 10), button_color=('white', '#2f89fc')), sg.Button("导出到Sheet", pad=(10, 10), button_color=('white', '#2f89fc'))]
]

# 创建窗口
window = sg.Window("含平面型缺陷管体拉伸应变承载能力评价", layout, background_color="white", resizable=True)

# 事件循环
while True:
    event, values = window.read()
    if event == sg.WINDOW_CLOSED:
        break
    elif event == """计算TSCerack (%)""":
        D= float(values['-DIAMETER-'])
        P = float(values['-PRESSURE-'])
        c = float(values['-DEFECT_LENGTH-'])
        a = float(values['-DEFECT_DEPTH-'])
        t = float(values['-THICKNESS-'])
        thetaA = float(values['-CTOD-'])
        thetay = float(values['-YIELD_STRENGTH-'])
        thetaU = float(values['-TENSILE_STRENGTH-'])
        fp = P / ((thetay * 2 * t) / D)
        TSC = cal_tsc(c, a, t, fp, thetaA, thetay, thetaU)
        TSC = round(TSC*100,2)
        
        #print('fp',fp)
        ryt = round(thetay/thetaU,2)
        window['-Y_T_RATIO-'].update(str(ryt))
        window['-TSC-'].update(str(TSC))
        window['-PRESSURE_FACTOR-'].update(str(round(fp,2)))
        #sg.popup("计算功能尚未实现")
        numbers = [D, t, thetaA,thetay, thetaU, ryt, P, fp, c, a, TSC]
        output = [round(float(num), 2) for num in numbers]
    elif event == "导出到Sheet":
        # 在这里添加导出到Sheet的逻辑
        try:
            # 下载或者创建一个新的工作簿和工作表
            file_name = "管道数据TSCcrack.xlsx"
            wb = load_workbook(file_name) if os.path.exists(file_name) else Workbook()
            wb.save("管道数据TSCcrack_bak.xlsx")
            # 创建一个新的工作簿和工作表

            ws = wb.active
            #ws.title = "管道数据_TSC"

            if "管道数据_TSCcrack" not in wb.sheetnames:
                print('')
                ws = wb.create_sheet("管道数据_TSCcrack")
                headers = [
                    ["编号", "管道几何尺寸","", "材料力学性能参数", "", "","", "运行压力", "压力因子", "体积型缺陷尺寸", "", "TSCcrack","填表人","填表日期", "备注"],
                    ["", "管道外直径","壁厚", "管道屈服强度", "管道抗拉强度", "屈强比", "CTOD","", "", "缺陷长度", "缺陷深度", "", "", "", ""],
                    ["", "mm", "mm","MPa", "MPa", "MPa/MPa", "mm","MPa", "MPa/MPa", "mm", "mm", "%","","","",]
                ]
                #ws.append(headers)

                for row in headers:
                    ws.append(row)

                # 合并单元格（例如，合并A1到C1）
                merge_list = ['A1:A3','B1:C1','D1:G1','H1:H2','I1:I2','J1:K1','L1:L2','M1:M3','N1:N3','O1:O3',]
                for merge_cells in merge_list:
                    ws.merge_cells(merge_cells)
            else:
                ws = wb["管道数据_TSCcrack"]
            row_count = len(ws["A"]) - 2
            ws.append([row_count] + output)

            color_cell = ['B1','B2','B3','C1','C2','C3',
                          'H1','H3','I1','I3','L1','L3','N1','N3']

            new_color_cell = [i+str(row_count) for i in ['B','C','H','I','L','N']]
            color_cell = color_cell + new_color_cell
            # 设置所有单元格的字体为居中加粗
            for row in ws.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.font = Font(bold=True)
                    # 设置实线边框
                    cell.border = Border(left=Side(style='thin'),
                                         right=Side(style='thin'),
                                         top=Side(style='thin'),
                                         bottom=Side(style='thin'))
                    #设置背景色为浅绿色(如果是合并后的单元格，使用左上角单元格编号)
                    if cell.coordinate in color_cell:
                        cell.fill = PatternFill(start_color="F0FFF0", end_color="F0FFF0", fill_type="solid")

            # 保存工作簿
            wb.save(file_name)
        except Exception as e:
            print(f"导出失败：{str(e)}")
        #sg.popup("导出功能尚未实现")
window.close()