import os
import random
import pickle
import warnings
import numpy as np
import pandas as pd
from xgboost import XGBRegressor
from scipy.interpolate import griddata

seed = 2024
random.seed(seed)
np.random.seed(seed)

# 忽略 DeprecationWarning 警告
warnings.filterwarnings("ignore", category=DeprecationWarning)

file_path = 'database_TSC.xlsx'
data = pd.read_excel(file_path,header=0)
data.columns = ['Wc','Lc','dc','TSC']

param = {
'EnterInternalPressure': 10,
    'PipeYieldStrength':528,
    'PipeTensileStrength':636,
'OuterDiameter':1422,
'WallThickness':32.1,
'DefectLength':72,
'DefectWidth':72,
'DefectDepth':3.21,
'CalOut':'CSC',
}

def validate_parameters(*args):
    for inx, param in enumerate(args):
        if inx == 5 and param not in ['TSC','CSC']:
            return 201, f"'{desc[inx]}' must be TSC or CSC"
        else:
            return 200, 'success'
            
        
        if not isinstance(param, (int, float)):
            return 202, f"'{desc[inx]}' must be a number"
        
        if param <= 0:
            return 203, f"'{desc[inx]}' must be non-negative"
        
    return 200, 'success'

def calculate(Wc, Lc, Dc, D, t, ryt, cal='TSC'):
    print('Raw input',Wc, Lc, Dc)
    Wc = float(Wc)
    Lc = float(Lc)
    Dc = float(Dc)
    Wc /= float((D*t)**0.5)
    Lc /= float((D*t)**0.5)
    Dc /= float(t)
    input_data = [Wc,Lc,Dc]
    


    points = data.iloc[:,:3]#实际点坐标
    values = data.iloc[:,3]#实际点的值

    xi = [i for i in input_data]
    tsc_ref = griddata(points, values, xi, method='linear')#插值计算，计算出插值点的值
    print('grid model input',round(Wc,4),round(Lc,4),round(Dc,4))
    print('grid model output',tsc_ref[0])
    tsc = tsc_ref[0]
    if np.isnan(tsc_ref[0]):
        #print(f'{xi} ---- > 插值超出范围')

        # 加载模型
        with open('xgboost_model_V0_09687.pkl', 'rb') as file:
            model = pickle.load(file)

        # 使用加载的模型进行预测
        tsc_ref = model.predict(np.array([xi]))
        print('ML model input',round(Wc,4),round(Lc),round(Dc))
        print('ML model output',tsc_ref[0])
        if ryt <= 0.83:
            tsc = tsc_ref[0]
        elif 0.83 < ryt :
            if cal == 'TSC':
                tsc = tsc_ref[0] * (-7.12 * ryt + 6.92)
            else:
                #tsc = 539.3 * tsc_ref[0] * (2.87 - 2.13 * ryt) * ((D * t) ** (-1.6))
                if fp >= 0.72:
                    tsc = 539.3 * tsc_ref[0] * (2.89 - 2.1 * ryt) * ((D * t) ** (-1.54))
                else:
                    tsc = tsc_ref[0] * (2.87 - 2.1 * ryt) * (452 * fp + 213 ) * ((D * t) ** (-1.54))
#         else:
#             tsc =0 # 如果 RYT 超过 0.92，可设为无效

    #print('-'*50)
    return tsc

def main(param):
    # 获取输入值
    D = param.get('OuterDiameter') or 1422
    t = param.get('WallThickness') or 32.1
    P = param.get('EnterInternalPressure')  or 10
    Y = param.get('PipeYieldStrength') or 528
    T = param.get('PipeTensileStrength') or 636
    cal = param.get('CalOut') or 'TSC'

    if cal == 'CSC':
        global data
        file_path = 'database_CSC.xlsx'
        data = pd.read_excel(file_path,header=0)
        data.columns = ['Wc','Lc','dc','TSC']
    myargs = [D, t, P, Y, T, cal]
    desc = ['OuterDiameter', 'WallThickness', 'EnterInternalPressure', 
            'PipeYieldStrength', 'PipeTensileStrength','CalOut']



    results = {'code':200,
              'data':{},
              'param':param,
              'message':'success'}
    code, mes = validate_parameters(*myargs)
    if code == 200:
        # 缺陷归一化


        #计算的压力因子
        fp = P / ((Y * 2 * t) / D)
        #屈强比
        ryt = Y / T

        Wc = param.get('DefectWidth') or 72
        Lc = param.get('DefectLength') or 72
        Dc = param.get('DefectDepth') or 3.21

        tsc = calculate(Wc, Lc, Dc, D, t, ryt, cal)
        print('11',tsc)
        results['data'] = {cal:tsc,'PF':fp,'Ryt':ryt}
    else:
        results['code'] = code
        results['message'] = mes
    return results

if '__name__' == '__mian__':
    results = main(param)