#!/usr/bin/env python
# coding: utf-8


import os
import random
import pickle
import warnings
import numpy as np
import pandas as pd
from xgboost import XGBRegressor
from scipy.interpolate import griddata

seed = 2024
random.seed(seed)
np.random.seed(seed)

# 忽略 DeprecationWarning 警告
warnings.filterwarnings("ignore", category=DeprecationWarning)

file_path = 'database_TSC.xlsx'
data = pd.read_excel(file_path,header=0)
data.columns = ['Wc','Lc','dc','TSC']

# 加载模型
with open('xgboost_model_V0_09687.pkl', 'rb') as file:
    model = pickle.load(file)
    
points = data.iloc[:,:3]#实际点坐标
values = data.iloc[:,3]#实际点的值

param = {
    'Sample' : 10, #抽样数量
    'Reliability':0.5, #失效概率基值
    'Adaptability':0.00035,#可靠度评价基值
    'EnterInternalPressure_Mean': 9.16,  # 输入内压均值
    'EnterInternalPressure_Standard_Deviation': 0.916,  # 输入内压标准差
    'PipeYieldStrength_Mean': 528,  # 管道屈服强度均值
    'PipeYieldStrength_Standard_Deviation': 21.12,  # 管道屈服强度标准差
    'PipeTensileStrength_Mean': 625,  # 管道抗拉强度均值
    'PipeTensileStrength_Standard_Deviation': 12.5,  # 管道抗拉强度标准差
    'OuterDiameter_Mean': 1422,  # 管道外径均值
    'OuterDiameter_Standard_Deviation': 42.66,  # 管道外径标准差
    'WallThickness_Mean': 32.1,  # 管道壁厚均值
    'WallThickness_Standard_Deviation': 1.605,  # 管道壁厚标准差
    'DefectLength_Mean': 30,  # 裂纹长度均值
    'DefectLength_Standard_Deviation': 0.9,  # 裂纹长度标准差
    'DefectDepth_Mean': 6,  # 裂纹深度均值
    'DefectDepth_Standard_Deviation': 0.6,  # 裂纹深度标准差
    'DefectWidth_Mean': 20,  #宽度均值
    'DefectWidth_Std':0.6,  #宽度标准差
    'YieldStrengthRatio_Mean': 0.902,  # 屈强比均值
    'YieldStrengthRatio_Standard_Deviation': 0.0916,  # 屈强比标准差
    'CalOut': 'CSC',  # 计算输出标识，具体含义依场景而定
}

def generate_normal_array(mean: float, std_dev: float, size: int) -> np.ndarray:
    """
    生成符合指定样本均值和标准差的正态分布数组
    
    参数:
        mean (float): 目标样本均值
        std_dev (float): 目标样本标准差（非负数）
        size (int): 输出数组长度（必须为正整数）
    
    返回:
        np.ndarray: 符合要求的正态分布数组
    
    异常:
        ValueError: 参数不符合要求时抛出
    """
    # 参数验证
    if not isinstance(size, int) or size <= 0:
        raise ValueError("数量必须为正整数")
    if std_dev < 0:
        raise ValueError("标准差必须为非负数")
    if size == 1 and std_dev != 0:
        raise ValueError("当数量为1时，标准差必须为0")
    
    # 处理特殊情况（size=1）
    if size == 1:
        return np.array([mean])
    
    # 生成标准正态分布数据
    data = np.random.randn(size)
    
    # 标准化并调整到目标参数
    data = (data - np.mean(data)) / np.std(data)  # 标准化到均值0，标准差1
    data = data * std_dev + mean                   # 调整到目标参数
    
    data = np.round(data, 2) ## s数组保留2位小数（四舍五入）
    return data

def validate_parameters(*args):
    for inx, param in enumerate(args):
        if inx == 19 and param not in ['TSC','CSC']:
            return 201, f"'{desc[inx]}' must be TSC or CSC"
        else:
            return 200, 'success'
            
        
        if not isinstance(param, (int, float)):
            return 202, f"'{desc[inx]}' must be a number"
        
        if param <= 0:
            return 203, f"'{desc[inx]}' must be non-negative"
        
    return 200, 'success'

def calculate(Wc, Lc, d, D, t, ryt, fp, cal = 'TSC', model = model, points = points, values = values):
    
    Wc /= float((D*t)**0.5)
    Lc /= float((D*t)**0.5)
    d /= float(t)
    input_data = [Wc,Lc,d]

    #xi = [i for i in input_data]
    xi = input_data
    tsc_ref = griddata(points, values, xi, method='linear')#插值计算，计算出插值点的值
    tsc = tsc_ref[0]
    if np.isnan(tsc_ref[0]):
        # 使用加载的模型进行预测
        tsc_ref = model.predict(np.array([xi]))

        if ryt <= 0.83:
            tsc = tsc_ref[0]
            #print('ryt <= 0.83',tsc)
        elif 0.83 < ryt: #或者这个地方能否给修改一下，ryt超过0.92按照0.92计算
            if cal == 'TSC':
                tsc = tsc_ref[0] * (-7.12 * ryt + 6.92)
                #print('0.83 < ryt tsc',tsc)
            else:
                if fp >= 0.72:
                    tsc = 539.3 * tsc_ref[0] * (2.89 - 2.1 * ryt) * ((D * t) ** (-1.54))
                else:
                    tsc = tsc_ref[0] * (2.87 - 2.1 * ryt) * (452 * fp + 213 ) * ((D * t) ** (-1.54))
                #print('0.83 < ryt csc',tsc)
                #tsc = 539.3 * tsc_ref[0] * (2.87 - 2.13 * ryt) * ((D * t) ** (-1.6))
        #print('model',tsc)
        
    return tsc

def main(param):
    # 获取输入值
    
    sample =param.get('Sample') or 10 #默认计算TSC
    rel = param.get('Reliability') or 0.5
    ada = param.get('Adaptability') or 0.00035
    cal = param.get('CalOut') or 'TSC' #默认计算TSC
    EnterInternalPressure_Mean = param.get('EnterInternalPressure_Mean') or 9.16
    EnterInternalPressure_Std = param.get('EnterInternalPressure_Standard_Deviation') or 0.916
    PipeYieldStrength_Mean = param.get('PipeYieldStrength_Mean') or 528
    PipeYieldStrength_Std = param.get('PipeYieldStrength_Standard_Deviation') or 21.12
    PipeTensileStrength_Mean = param.get('PipeTensileStrength_Mean') or 625
    PipeTensileStrength_Std = param.get('PipeTensileStrength_Standard_Deviation') or 12.5
    OuterDiameter_Mean = param.get('OuterDiameter_Mean') or 1422
    OuterDiameter_Std = param.get('OuterDiameter_Standard_Deviation') or 42.66
    WallThickness_Mean = param.get('WallThickness_Mean') or 32.1
    WallThickness_Std = param.get('WallThickness_Standard_Deviation') or 1.605
    DefectLength_Mean = param.get('DefectLength_Mean') or 30
    DefectLength_Std = param.get('DefectLength_Standard_Deviation') or 0.9
    DefectDepth_Mean = param.get('DefectDepth_Mean') or 6
    DefectDepth_Std = param.get('DefectDepth_Standard_Deviation') or 0.6
    DefectWidth_Mean = param.get('DefectWidth_Mean') or 20
    DefectWidth_Std = param.get('DefectWidth_Standard_Deviation') or 0.6
    YieldStrengthRatio_Mean = param.get('YieldStrengthRatio_Mean') or 0.902
    YieldStrengthRatio_Std = param.get('YieldStrengthRatio_Standard_Deviation') or 0.0916
    
    
    if cal == 'CSC':
        file_path = 'database_CSC.xlsx'
        data = pd.read_excel(file_path,header=0)
        data.columns = ['Wc','Lc','dc','TSC']

#     myargs = [D, t, P, Y, T, cal]
#     desc = ['OuterDiameter', 'WallThickness', 'EnterInternalPressure', 
#             'PipeYieldStrength', 'PipeTensileStrength','CalOut']

    myargs = [EnterInternalPressure_Mean, EnterInternalPressure_Std, PipeYieldStrength_Mean, PipeYieldStrength_Std,
              PipeTensileStrength_Mean, PipeTensileStrength_Std, OuterDiameter_Mean, OuterDiameter_Std,
              WallThickness_Mean, WallThickness_Std, DefectLength_Mean, DefectLength_Std, DefectDepth_Mean,
              DefectDepth_Std, DefectWidth_Mean,DefectWidth_Std,
              YieldStrengthRatio_Mean, YieldStrengthRatio_Std, cal]
    
    
    desc = ["EnterInternalPressure_Mean", "EnterInternalPressure_Standard_Deviation", "PipeYieldStrength_Mean",
            "PipeYieldStrength_Standard_Deviation", "PipeTensileStrength_Mean", "PipeTensileStrength_Standard_Deviation",
            "OuterDiameter_Mean", "OuterDiameter_Standard_Deviation", "WallThickness_Mean", "WallThickness_Standard_Deviation",
            "DefectLength_Mean", "DefectLength_Standard_Deviation", "DefectDepth_Mean", "DefectDepth_Standard_Deviation",
            "DefectWidth_Mean","DefectWidth_Std",
             "YieldStrengthRatio_Mean", "YieldStrengthRatio_Standard_Deviation", "CalOut"]


    results = {'code':200,
              'data':{},
              'param':param,
              'message':'success'}
    
    code, mes = validate_parameters(*myargs)
    
    if code == 200:
        # 生成正态分布数组，使用之前定义的变量作为均值和标准差
        D_sample = generate_normal_array(mean=OuterDiameter_Mean, std_dev=OuterDiameter_Std, size=sample)
        P_sample = generate_normal_array(mean=EnterInternalPressure_Mean, std_dev=EnterInternalPressure_Std, size=sample)
        Lc_sample = generate_normal_array(mean=DefectLength_Mean, std_dev=DefectLength_Std, size=sample)  # 长度
        d_sample = generate_normal_array(mean=DefectDepth_Mean, std_dev=DefectDepth_Std, size=sample)  # 深度
        t_sample = generate_normal_array(mean=WallThickness_Mean, std_dev=WallThickness_Std, size=sample)
        w_sample = generate_normal_array(mean=DefectWidth_Mean, std_dev=DefectWidth_Std, size=sample)  # 这里没有对应变量，保持原样
        thetay_sample = generate_normal_array(mean=PipeYieldStrength_Mean, std_dev=PipeYieldStrength_Std, size=sample)
        thetaU_sample = generate_normal_array(mean=PipeTensileStrength_Mean, std_dev=PipeTensileStrength_Std, size=sample)       

        #计算的压力因子
        ryt_sample = thetay_sample/thetaU_sample
        
        # 核心计算（带分母非零验证）
        denominator_sample = (thetay_sample * 2 * t_sample) / D_sample
        if np.any(denominator_sample == 0):
            raise ZeroDivisionError("分母存在零值，请检查输入数据")
            
        fp_sample = P_sample / denominator_sample
        
        TSC = []
        TSC_labels = []
        for i in range(sample):
            TSC_i = calculate(w_sample[i], Lc_sample[i], d_sample[i], D_sample[i], t_sample[i], ryt_sample[i], fp_sample[i], cal)
            TSC.append(TSC_i)
            label = 1 if TSC_i > rel else 0
            TSC_labels.append(label)
            
        failure = sum(TSC_labels) / sample  # 假设失效概率为0.8

        if failure > ada:
            reliability = "不安全" 
        elif failure < ada:
            reliability = "安全" 
        else:
            reliability = "临界"
            
        failure = round(failure, 5)  # 假设失效概率为0.8
            
        results['data'] = {'failure':failure,'reliability':reliability}
    else:
        results['code'] = code
        results['message'] = mes
    return results

if '__name__' == '__mian__':
    results = main(param)