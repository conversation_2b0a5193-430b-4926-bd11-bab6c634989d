#!/usr/bin/env python
# coding: utf-8

# In[ ]:


import os
import time
import PySimpleGUI as sg
from datetime import datetime
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font, PatternFill,Border, Side

formatted_date = datetime.now().strftime("%Y-%m-%d")

def cal_tsc(c, a, t, fp, thetaA, thetay, thetaU):
    """
    c = 1  #裂纹长度
    a = 1  #裂纹深度
    t = 1  #管道壁厚
    thetay = 1  #屈服强度
    thetaU = 1  #抗拉强度
    
    """
    c = float(c)
    a = float(a)
    t = float(t)
    thetaA = float(thetaA)
    thetay = float(thetay)
    thetaU = float(thetaU)
    # a列
    a1 = 2.084E+00
    a2 = 2.812E-01
    a3 = -4.950E-01
    a4 = 7.373E-01
    a5 = -5.005E+00
    a6 = 1.186E+00
    a7 = 1.644E+00
    a8 = 7.374E-01
    a9 = -9.829E-01
    a10 = 8.655E-02
    a11 = -1.029E-01
    a12 = -1.500E-01
    a13 = 1.025E+00
    a14 = 5.557E+00

    # b列
    b1 = -5.005E-02
    b2 = -5.139E-03
    b3 = 4.485E-01
    b4 = 1.417E+00
    b5 = 2.217E+00
    b6 = 1.029E+00
    b7 = -2.598E+00
    b8 = -2.679E+00
    b9 = 1.694E+00

    # c列
    c1 = 1.409E+00
    c2 = 2.345E-01
    c3 = 1.125E+00
    c4 = 4.181E+00
    c5 = 1.201E+00
    c6 = -5.384E+00
    c7 = 2.406E+00
    c8 = -2.154E-01
    c9 = -5.237E-03
    c10 = 9.889E+00
    c11 = 3.547E-01
    c12 = -7.513E-01

    # d列
    d1 = 2.209E-02
    d2 = 1.156E+00
    d3 = 1.601E+00
    d4 = 8.964E-01
    d5 = 1.383E+00
    d6 = 1.333E+00
    d7 = 9.313E-02
    d8 = -2.240E+00
    d9 = 8.559E-00
    d10 = -3.719E+00

    e = 2.71 #自然常数
    h = 1
    phi = 1

    ########### 以上是常量

    beta, eta, psi, xi = 2*c/t, a/t, h/t, thetay/thetaU

    # Define the equations
    A = (a1 * (e ** (a2 / beta)) * (e ** (a3 * eta * beta * (e ** (a4 / beta)))) *
         (1 + a5 * (psi ** a6) + a7 * psi * (eta * beta) ** a8) *
         (1 + a9 * (xi ** a10) * (phi ** a11) + a12 * (psi ** a13) * (xi ** a14)))

    B = (beta ** b1 * (eta ** (b2 * (beta ** b3) / eta)) *
         (b4 * (phi ** b5) * ((b6 * (phi ** b7)) ** xi) + b8 * (psi ** b9)))

    C = (e ** (c1 / beta) * (e ** ((c2 * beta) / ((1 + c3 * beta) * eta))) *
         (1 + c4 * (psi ** c5) + c6 * psi * (eta ** -eta) + c7 * psi * (eta ** -beta)) *
         (c8 + c9 * (phi ** 10) + c11 * (xi ** c12) * phi))

    D = (d1 * (beta ** d2) * (eta ** ((d3 * beta) / (1 + d4 * beta))) *
         (1 + d5 * (psi ** d6)) * (1 + d7 * (xi ** d8) + d9 * (phi ** 10)))

    TSCp = A * (C*thetaA) ** (B*(thetaA**D))
    
    if 0.6<=fp<=0.8:
        return TSCp
    else:
        TSC0 = 1.5 * TSCp
        return TSC0 + (5 * fp/3) * (TSCp - TSC0)
    return TSCp

import numpy as np

def generate_normal_array(mean: float, std_dev: float, size: int) -> np.ndarray:
    """
    生成符合指定样本均值和标准差的正态分布数组
    
    参数:
        mean (float): 目标样本均值
        std_dev (float): 目标样本标准差（非负数）
        size (int): 输出数组长度（必须为正整数）
    
    返回:
        np.ndarray: 符合要求的正态分布数组
    
    异常:
        ValueError: 参数不符合要求时抛出
    """
    # 参数验证
    if not isinstance(size, int) or size <= 0:
        raise ValueError("数量必须为正整数")
    if std_dev < 0:
        raise ValueError("标准差必须为非负数")
    if size == 1 and std_dev != 0:
        raise ValueError("当数量为1时，标准差必须为0")
    
    # 处理特殊情况（size=1）
    if size == 1:
        return np.array([mean])
    
    # 生成标准正态分布数据
    data = np.random.randn(size)
    
    # 标准化并调整到目标参数
    data = (data - np.mean(data)) / np.std(data)  # 标准化到均值0，标准差1
    data = data * std_dev + mean                   # 调整到目标参数
    
    return data


progress_bar_max = 1
BAR_COLORS = ('#2f89fc', sg.theme_background_color())  # (前景色, 背景色)
# 定义布局
layout = [
    [sg.Text("含平面型缺陷管体拉伸应变承载能力评价(TSCcrack)", 
             justification='center', size=(90, 1), background_color='#2f89fc', font=('Arial', 15, 'bold'),
             text_color="white", pad=(0, 0))],
    
    [sg.Frame(
        "填写信息",
        [
            [sg.Text("填表人：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Input(key="-NAME-", expand_x=True, pad=(10, 10))],
            [sg.Text("填写日期：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Input(key="-DATE-", default_text=formatted_date, expand_x=True, pad=(10, 10))],
            [sg.Text("备注信息：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Multiline(key="-REMARK-", size=(10, 5), expand_x=True, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    )],
    
    [sg.Frame(
        "管道几何结构",
        [
            [sg.Text("管道外径(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 1422", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 42.66", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("管道壁厚(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 32.1", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 1.605", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    ),
    sg.Frame(
        "运行压力",
        [
            [sg.Text("内压", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 9.16", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0.916", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    )],
    
    [sg.Frame(
        "管道力学性能",
        [
            [sg.Text("屈强比", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 0.902", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0.0916", size=(11, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("屈服强度(MPa)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 528", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 21.12", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("抗拉强度(MPa)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 625", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 12.5", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    ),
    sg.Frame(
        "缺陷几何",
        [
            [sg.Text("裂纹长度(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 20", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 1", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("裂纹深度(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 3", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0.3", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("CTOD值(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 0.18", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0.01", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    )],
    [
        sg.Column([
            [sg.Text("应变需求", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(default_text="0.005", key="-RELIABILITY 0-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white'),
        sg.Column([
            [sg.Text("目标可靠度", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(default_text="0.00035", key="-ADAPTABILITY-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white')
    ],
    
    # 第二行：失效概率 + 可靠度评价（并排）
    [
        sg.Column([
            [sg.Text("失效概率", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(key="-FAILURE-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white'),
        sg.Column([
            [sg.Text("可靠度评价", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(key="-RELIABILITY-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white')
    ],
    
    # 第三行：抽样数量 + 操作按钮（并排）
    [
        sg.Column([
            [sg.Text("抽样数量", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(default_text="10", key="-SAMPLE-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white'),
        sg.Column([
             [sg.Button("计算", pad=(10,10), button_color=('white','#2f89fc'),size=(12,1)),
             sg.Button("导出到Sheet", pad=(10,10), button_color=('white','#2f89fc'),size=(12,1))]
        ],background_color='white')
    ],
    
    [sg.ProgressBar(progress_bar_max, orientation='h', size=(30.5, 20), key='-PROGRESS-', bar_color=BAR_COLORS),sg.Text('0%', size=(5, 1), key='-PERCENT-')]
    
]

# 创建窗口
window = sg.Window("含平面型缺陷管体拉伸应变承载能力评价(TSCcrack)", layout, background_color="white", resizable=True)

# 启动事件循环
while True:
    event, values = window.read()
    if event == sg.WINDOW_CLOSED:
        break
    elif event == "计算":
        # 假设计算逻辑（这里直接指定结果）
        sample = int(values['-SAMPLE-'])
        input_name = values['-NAME-']
        input_date = values['-DATE-']
        input_remark = values['-REMARK-']
        
        if int(sample)<=0:
            sample = 10
        D_sample = generate_normal_array(mean=1422, std_dev=0, size=sample)
        P_sample = generate_normal_array(mean=9.16, std_dev=0, size=sample)
        c_sample = generate_normal_array(mean=20, std_dev=1, size=sample)
        a_sample = generate_normal_array(mean=3, std_dev=0.3, size=sample)  # 假设计算逻辑（这里直接指定结果）
        t_sample = generate_normal_array(mean=32.1, std_dev=0, size=sample)
        thetaA_sample = generate_normal_array(mean=0.18, std_dev=0.01, size=sample)
        thetay_sample = generate_normal_array(mean=528, std_dev=0, size=sample)
        thetaU_sample = generate_normal_array(mean=625, std_dev=0, size=sample)
        
        ryt_sample = thetay_sample/thetaU_sample
        
        # 核心计算（带分母非零验证）
        denominator_sample = (thetay_sample * 2 * t_sample) / D_sample
        if np.any(denominator_sample == 0):
            raise ZeroDivisionError("分母存在零值，请检查输入数据")
            
        fp_sample = P_sample / denominator_sample
        TSCcrack = []
        TSCcrack_labels = []
        
        progress = 0
        gap =  progress_bar_max/sample
        start_time = time.time()
        
        for i in range(sample):
            TSCcrack_i = cal_tsc(c_sample[i], a_sample[i], t_sample[i], fp_sample[i], thetaA_sample[i], thetay_sample[i], thetaU_sample[i])
                          #cal_tsc(c, a, t, fp, thetaA, thetay, thetaU)
            TSCcrack.append(TSCcrack_i)
            label = 1 if TSCcrack_i > 0.02 else 0
            TSCcrack_labels.append(label)
            
            # 模拟进度更新
            if progress < progress_bar_max:
                progress += gap
                window['-PROGRESS-'].update(progress)  # 更新进度条
                percent = int((progress / progress_bar_max) * 100)
                window['-PERCENT-'].update(f'{percent}%')  # 更新文本内容
            else:
                window['-PROGRESS-'].update(progress_bar_max)  # 确保进度条填满
                window['-PERCENT-'].update(f'{100}%')  # 更新文本内容
        
        failure = sum(TSCcrack_labels) / sample  # 假设失效概率为0.8
        #基准值为0.00035，失效概率＞0.00035，输出不安全；失效概率=0.00035，输出临界；失效概率<0.00035，输出安全
        base = 0.00035
        if failure > base:
            reliability = "不安全" 
        elif failure < base:
            reliability = "安全" 
        else:
            reliability = "临界" 
            
        failure = round(failure, 5)  # 假设失效概率为0.8
        
        # 更新界面
        window['-FAILURE-'].update(failure)
        window['-RELIABILITY-'].update(reliability)
#         numbers = [D, t, thetaA,thetay, thetaU, ryt, P, fp, c, a, TSC]
#         output = [round(float(num), 2) for num in numbers]
        
        output = zip(D_sample, t_sample, thetay_sample, thetaU_sample, ryt_sample, thetaA_sample, 
                     P_sample, fp_sample, c_sample, a_sample, TSCcrack,
                    [input_name] * sample,[input_date] * sample,[input_remark] * sample)
        output = [list(rw) for rw in output]
        #print(output)
    elif event == "导出到Sheet":
        # 在这里添加导出到Sheet的逻辑
        try:
            # 下载或者创建一个新的工作簿和工作表
            myname = f'随机抽样管道数据TSCcrack'
            file_name = f"{myname}.xlsx"
            wb = load_workbook(file_name) if os.path.exists(file_name) else Workbook()
            wb.save(f"{myname}_bak.xlsx")
            # 创建一个新的工作簿和工作表

            ws = wb.active
            #ws.title = "管道数据_TSC"

            if myname not in wb.sheetnames:
                #print('')
                ws = wb.create_sheet(myname)
                headers = [
                    ["编号", "管道几何尺寸","", "材料力学性能参数", "", "","", "运行压力", "压力因子", "体积型缺陷尺寸", "", "TSCcrack","填表人","填表日期", "备注"],
                    ["", "管道外直径","壁厚", "管道屈服强度", "管道抗拉强度", "屈强比", "CTOD","", "", "裂纹长度", "裂纹深度", "", "", "", ""],
                    ["", "mm", "mm","MPa", "MPa", "MPa/MPa", "mm","MPa", "MPa/MPa", "mm", "mm", "%","","","",]
                ]
                #ws.append(headers)

                for row in headers:
                    ws.append(row)

                # 合并单元格（例如，合并A1到C1）
                merge_list = ['A1:A3','B1:C1','D1:G1','H1:H2','I1:I2','J1:K1','L1:L2','M1:M3','N1:N3','O1:O3',]
                for merge_cells in merge_list:
                    ws.merge_cells(merge_cells)
            else:
                ws = wb[myname]
            row_count = len(ws["A"]) - 2
            new_color_cell = [i+str(j) for j in range(row_count,row_count+sample+3) for i in ['B','C','G','H','L','N']]
            progress = 0
            gap =  progress_bar_max/sample
            start_time = time.time()
            for row_out in output:
                ws.append([row_count] + row_out)
                row_count += 1
                # 模拟进度更新
                if progress < progress_bar_max:
                    progress += gap
                    window['-PROGRESS-'].update(progress)  # 更新进度条
                    percent = int((progress / progress_bar_max) * 100)
                    window['-PERCENT-'].update(f'{percent}%')  # 更新文本内容
                else:
                    window['-PROGRESS-'].update(progress_bar_max)  # 确保进度条填满
                    window['-PERCENT-'].update(f'{100}%')  # 更新文本内容

            color_cell = ['B1','B2','B3','C1','C2','C3',
                          'H1','H3','I1','I3','L1','L3','N1','N3']

            #new_color_cell = [i+str(row_count) for i in ['B','C','H','I','L','N']]
            color_cell = color_cell + new_color_cell
            # 设置所有单元格的字体为居中加粗
            for row in ws.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.font = Font(bold=True)
                    # 设置实线边框
                    cell.border = Border(left=Side(style='thin'),
                                         right=Side(style='thin'),
                                         top=Side(style='thin'),
                                         bottom=Side(style='thin'))
                    #设置背景色为浅绿色(如果是合并后的单元格，使用左上角单元格编号)
                    if cell.coordinate in color_cell:
                        cell.fill = PatternFill(start_color="F0FFF0", end_color="F0FFF0", fill_type="solid")

            # 保存工作簿
            wb.save(file_name)
        except Exception as e:
            sg.popup("导出失败", f"{str(e)}")
            raise SystemExit(f"错误: {str(e)}")
window.close()


# In[ ]:




