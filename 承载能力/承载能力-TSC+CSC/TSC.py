import os
import warnings
from PyQt5.QtWidgets import (
    QApplication, QWidget, QLabel, QLineEdit, QPushButton, QVBoxLayout, QHBoxLayout, QGridLayout, QGroupBox, QTextEdit
)
from PyQt5.QtCore import Qt, QTimer
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font, PatternFill,Border, Side

from scipy.interpolate import griddata
from xgboost import XGBRegressor
import pandas as pd
import numpy as np
import random
import pickle

seed = 2024
random.seed(seed)
np.random.seed(seed)
warnings.filterwarnings("ignore", category=DeprecationWarning)

file_path = 'database_TSC.xlsx'
data = pd.read_excel(file_path,header=0)
data.columns = ['Wc','Lc','dc','TSC']

class 拉伸应变能力计算工具(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("拉伸应变能力计算工具")
        self.setFixedSize(1200, 800)
        self.setStyleSheet("background-color: white;")

        main_layout = QVBoxLayout()

# 忽略 DeprecationWarning 警告e("拉伸应变能力计算工具")
        self.setFixedSize(1200, 800)
        self.setStyleSheet("background-color: white;")

        main_layout = QVBoxLayout()

        # 标题
        title_label = QLabel("拉伸应变能力计算工具")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet(
            "background-color: #2F75B5; color: white; font-size: 18px; font-weight: bold; padding: 10px;"
        )
        main_layout.addWidget(title_label)

        # 填表人和备注区域
        info_group = QGroupBox("填写信息")
        info_layout = QGridLayout()

        info_layout.addWidget(QLabel("填表人:"), 0, 0)
        self.prepared_by_input = QLineEdit()
        info_layout.addWidget(self.prepared_by_input, 0, 1)

        info_layout.addWidget(QLabel("填写日期:"), 1, 0)
        self.date_input = QLineEdit("2024/7/26")
        info_layout.addWidget(self.date_input, 1, 1)

        info_layout.addWidget(QLabel("备注:"), 2, 0)
        self.remark_input = QTextEdit()
        self.remark_input.setPlaceholderText("请输入备注信息...")
        info_layout.addWidget(self.remark_input, 2, 1, 1, 2)  # 占用两列

        info_group.setLayout(info_layout)
        main_layout.addWidget(info_group)

        # 输入部分
        input_layout = QGridLayout()

        # 管道几何
        pipe_geometry_group = self.create_section("管道几何结构", [
            ("管道外径 (mm)", QLineEdit("1422")),
            ("管道壁厚 (mm)", QLineEdit("32.1")),
        ])
        input_layout.addWidget(pipe_geometry_group, 0, 0)

        # 运行压力
        operating_pressure_group = self.create_section("运行压力", [
            ("运行压力 (MPa)", QLineEdit("10")),
            ("压力因子 fp", QLabel("")),
        ])
        input_layout.addWidget(operating_pressure_group, 0, 1)

        # 管道力学性能
        pipe_material_group = self.create_section("管道力学性能", [
            ("屈服强度 (MPa)", QLineEdit("528")),
            ("抗拉强度 (MPa)", QLineEdit("636")),
            ("屈强比 Y/T", QLabel("")),
        ])
        input_layout.addWidget(pipe_material_group, 1, 0)

        # 缺陷几何
        defect_geometry_group = self.create_section("缺陷几何", [
            ("缺陷纵向长度 Lc (mm)", QLineEdit("72")),
            ("缺陷横向长度 Wc (mm)", QLineEdit("72")),
            ("缺陷深度 Dc (mm)", QLineEdit("3.21")),
        ])
        input_layout.addWidget(defect_geometry_group, 1, 1)

        main_layout.addLayout(input_layout)

        # 底部按钮布局
        bottom_layout = QHBoxLayout()

        # 计算按钮
        self.calculate_button = QPushButton("计算 TSC")
        self.calculate_button.setStyleSheet(
            "padding: 10px; font-size: 12px; background-color: white; border: 1px solid gray;"
        )
        self.calculate_button.clicked.connect(self.calculate_all)
        bottom_layout.addWidget(self.calculate_button, alignment=Qt.AlignLeft)

        # TSC 显示
        tsc_group = QGroupBox("拉伸应变能力 (TSC)")
        tsc_group.setStyleSheet(
            "QGroupBox { font-weight: bold; font-size: 12px; color: black; border: 1px solid black; }"
        )
        tsc_layout = QHBoxLayout()
        self.tsc_label = QLabel("")
        self.tsc_label.setAlignment(Qt.AlignCenter)
        self.tsc_label.setStyleSheet(
            "font-size: 14px; font-weight: bold; color: black; padding: 10px; border: 1px solid black;"
        )
        tsc_layout.addWidget(self.tsc_label)
        tsc_group.setLayout(tsc_layout)
        bottom_layout.addWidget(tsc_group, alignment=Qt.AlignCenter)

        # 导出按钮
        self.export_button = QPushButton("导出到Sheet")
        self.export_button.setStyleSheet(
            "padding: 10px; font-size: 12px; background-color: white; border: 1px solid gray;"
        )
        self.export_button.clicked.connect(self.export_to_excel)
        bottom_layout.addWidget(self.export_button, alignment=Qt.AlignRight)

        main_layout.addLayout(bottom_layout)
        self.setLayout(main_layout)

    def create_section(self, title, fields):
        group_box = QGroupBox(title)
        layout = QGridLayout()
        for i, field in enumerate(fields):
            label, widget = field
            layout.addWidget(QLabel(label), i, 0)
            layout.addWidget(widget, i, 1)
            if isinstance(widget, QLineEdit) or isinstance(widget, QLabel):
                widget.setObjectName(label)
        group_box.setLayout(layout)
        return group_box

    def calculate_all(self):
        self.calculate_button.setEnabled(False)
        self.calculate_button.setText("计算中...")

        def complete_calculation():
            try:
                # 获取输入值
                D = float(self.findChild(QLineEdit, "管道外径 (mm)").text() or 1422)
                t = float(self.findChild(QLineEdit, "管道壁厚 (mm)").text() or 32.1)
                P = float(self.findChild(QLineEdit, "运行压力 (MPa)").text() or 10)
                Y = float(self.findChild(QLineEdit, "屈服强度 (MPa)").text() or 528)
                T = float(self.findChild(QLineEdit, "抗拉强度 (MPa)").text() or 636)

                fp = P / ((Y * 2 * t) / D)
                ryt = Y / T

                # 缺陷归一化
                wc = float(self.findChild(QLineEdit, "缺陷横向长度 Wc (mm)").text() or 72)
                lc = float(self.findChild(QLineEdit, "缺陷纵向长度 Lc (mm)").text() or 72)
                dc = float(self.findChild(QLineEdit, "缺陷深度 Dc (mm)").text() or 3.21)

                # 根据 RYT 判断公式
                tsc_ref = self.calculate(wc, lc, dc, D, t)
                #tsc_ref = 100  # 假设初始 TSCref 值为 100，可替换为实际逻辑
                if ryt <= 0.83:
                    tsc = tsc_ref
                elif 0.83 < ryt:
                    tsc = tsc_ref * (-7.13 * ryt + 6.918)
                # else:
                #     tsc = 0  # 如果 RYT 超过 0.92，可设为无效或另行处理
                    
                numbers = [D, t, Y, T, ryt, P, fp, lc, wc, dc, tsc]
                self.output = [round(float(num), 2) for num in numbers]

                # 更新界面显示
                self.findChild(QLabel, "压力因子 fp").setText(f"{fp:.2f}")
                self.findChild(QLabel, "屈强比 Y/T").setText(f"{ryt:.2f}")
                self.tsc_label.setText(f"TSC: {float(tsc):.2f}%")
            except ValueError:
                self.tsc_label.setText("输入无效，请检查数据！")
            finally:
                self.calculate_button.setEnabled(True)
                self.calculate_button.setText("计算 TSC")

        QTimer.singleShot(1000, complete_calculation)

    def export_to_excel(self):
        self.export_button.setEnabled(False)
        self.export_button.setText("导出成功")

        def complete_export():
            try:
                # 下载或者创建一个新的工作簿和工作表
                file_name = "管道数据.xlsx"
                wb = load_workbook(file_name) if os.path.exists(file_name) else Workbook()
                wb.save("管道数据_bak.xlsx")
                # 创建一个新的工作簿和工作表

                ws = wb.active
                #ws.title = "管道数据_TSC"

                if "管道数据_TSC" not in wb.sheetnames:
                    print('')
                    ws = wb.create_sheet("管道数据_TSC")
                    headers = [
                        ["编号", "管道几何尺寸","", "材料力学性能参数", "", "", "运行压力", "压力因子", "体积型缺陷尺寸", "", "", "TSC"],
                        ["", "管道外直径","壁厚", "管道屈服强度", "管道抗拉强度", "屈强比", "", "", "缺陷长度", "缺陷宽度", "缺陷深度", ""],
                        ["", "mm", "mm","MPa", "MPa", "MPa/MPa", "MPa", "MPa/MPa", "mm", "mm", "mm", "%"]
                    ]
                    #ws.append(headers)

                    for row in headers:
                        ws.append(row)

                    # 合并单元格（例如，合并A1到C1）
                    merge_list = ['A1:A3','B1:C1','D1:F1','G1:G2','H1:H2','I1:K1','L1:L2']
                    for merge_cells in merge_list:
                        ws.merge_cells(merge_cells)
                else:
                    ws = wb["管道数据_TSC"]
                row_count = len(ws["A"]) - 2
                ws.append([row_count] + self.output)

                color_cell = ['B1','B2','B3','C1','C2','C3',
                              'G1','G3','H1','H3','L1','L3']

                new_color_cell = [i+str(row_count) for i in ['B','C','G','H','L']]
                color_cell = color_cell + new_color_cell
                # 设置所有单元格的字体为居中加粗
                for row in ws.iter_rows():
                    for cell in row:
                        cell.alignment = Alignment(horizontal='center', vertical='center')
                        cell.font = Font(bold=True)
                        # 设置实线边框
                        cell.border = Border(left=Side(style='thin'),
                                             right=Side(style='thin'),
                                             top=Side(style='thin'),
                                             bottom=Side(style='thin'))
                        #设置背景色为浅绿色(如果是合并后的单元格，使用左上角单元格编号)
                        if cell.coordinate in color_cell:
                            cell.fill = PatternFill(start_color="F0FFF0", end_color="F0FFF0", fill_type="solid")

                # 保存工作簿
                wb.save(file_name)
            except Exception as e:
                print(f"导出失败：{str(e)}")
            finally:
                QTimer.singleShot(3000, self.reset_export_button)

        QTimer.singleShot(500, complete_export)

    def reset_export_button(self):
        self.export_button.setEnabled(True)
        self.export_button.setText("导出到Sheet")
        
    def calculate(self, Wc, Lc, d, D, t):
        print('Raw input',Wc, Lc, d)
        Wc = float(Wc)
        Lc = float(Lc)
        d = float(d)
        Wc /= float((D*t)**0.5)
        Lc /= float((D*t)**0.5)
        d /= float(t)
        input_data = [Wc,Lc,d]

        points = data.iloc[:,:3]#实际点坐标
        values = data.iloc[:,3]#实际点的值

        xi = [i for i in input_data]
        tsc_ref = griddata(points, values, xi, method='linear')#插值计算，计算出插值点的值
        print('grid model input',Wc,Lc,d)
        print('grid model output',tsc_ref[0])

        if np.isnan(tsc_ref[0]):
            #print(f'{xi} ---- > 插值超出范围')

            # 加载模型
            with open('xgboost_model_V0_09687.pkl', 'rb') as file:
                model = pickle.load(file)

            # 使用加载的模型进行预测
            # 假设 X_test 是测试数据
            tsc_ref = model.predict(np.array([xi]))
            print('ML model input',Wc,Lc,d)
            print('ML model output',tsc_ref[0])
#         if ryt <= 0.83:
#             tsc = tsc_ref[0]
#         elif 0.83 < ryt < 0.92:
#             tsc = tsc_ref[0] * (-7.14 * ryt + 6.92)
#         print('-'*50)
        return tsc_ref[0]

if __name__ == "__main__":
    import sys
    app = QApplication(sys.argv)
    window = 拉伸应变能力计算工具()
    window.show()
    sys.exit(app.exec_())