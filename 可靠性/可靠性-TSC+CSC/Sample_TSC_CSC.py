#!/usr/bin/env python
# coding: utf-8


import os
import warnings
from scipy.interpolate import griddata
from xgboost import XGBRegressor
from datetime import datetime
import FreeSimpleGUI as sg
import pandas as pd
import numpy as np
import pickle
import time
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font, PatternFill,Border, Side

formatted_date = datetime.now().strftime("%Y-%m-%d")


# 忽略 DeprecationWarning 警告
warnings.filterwarnings("ignore", category=DeprecationWarning)

# seed = 2024
# random.seed(seed)
# np.random.seed(seed)

file_path = 'database_TSC.xlsx'
data = pd.read_excel(file_path,header=0)
data.columns = ['Wc','Lc','dc','TSC']

# 加载模型
with open('xgboost_model_V0_09687.pkl', 'rb') as file:
    model = pickle.load(file)
    
points = data.iloc[:,:3]#实际点坐标
values = data.iloc[:,3]#实际点的值

def calculate1(Wc, Lc, d, D, t, ryt, model = model, points = points, values = values):
    
    input_data = [Wc,Lc,d]

    #xi = [i for i in input_data]
    xi = input_data
    tsc_ref = griddata(points, values, xi, method='linear')#插值计算，计算出插值点的值

    if np.isnan(tsc_ref[0]):
        # 使用加载的模型进行预测
        tsc_ref = model.predict(np.array([xi]))

    tsc_ref = tsc_ref[0]

    if ryt <= 0.83:
        tsc = tsc_ref
    elif 0.83 < ryt:
        tsc = tsc_ref * (-7.14 * ryt + 6.92)
#     else:
#         tsc = 0  # 如果 RYT 超过 0.92，可设为无效或另行处理
    return tsc

def calculate(Wc, Lc, d, D, t, ryt,fp, cal = 'TSC', model = model, points = points, values = values):
    
    Wc /= float((D*t)**0.5)
    Lc /= float((D*t)**0.5)
    d /= float(t)
    
    input_data = [Wc,Lc,d]

    #xi = [i for i in input_data]
    xi = input_data
    tsc_ref = griddata(points, values, xi, method='linear')#插值计算，计算出插值点的值
    tsc = tsc_ref[0]
    #print('grid',tsc)
    if np.isnan(tsc_ref[0]):
        # 使用加载的模型进行预测
        tsc_ref = model.predict(np.array([xi]))

        if ryt <= 0.83:
            tsc = tsc_ref[0]
            #print('ryt <= 0.83',tsc)
        elif 0.83 < ryt: #或者这个地方能否给修改一下，ryt超过0.92按照0.92计算
            if cal == 'TSC':
                tsc = tsc_ref[0] * (-7.12 * ryt + 6.92)
                #print('0.83 < ryt tsc',tsc)
            else:
                if fp >= 0.72:
                    tsc = 539.3 * tsc_ref[0] * (2.89 - 2.1 * ryt) * ((D * t) ** (-1.54))
                else:
                    tsc = tsc_ref[0] * (2.87 - 2.1 * ryt) * (452 * fp + 213 ) * ((D * t) ** (-1.54))
                #print('0.83 < ryt csc',tsc)
                #tsc = 539.3 * tsc_ref[0] * (2.87 - 2.13 * ryt) * ((D * t) ** (-1.6))
        #print('model',tsc)
#         else:
#             tsc =0 # 如果 RYT 超过 0.92，可设为无效
    return tsc

def generate_normal_array(mean: float, std_dev: float, size: int) -> np.ndarray:
    """
    生成符合指定样本均值和标准差的正态分布数组
    
    参数:
        mean (float): 目标样本均值
        std_dev (float): 目标样本标准差（非负数）
        size (int): 输出数组长度（必须为正整数）
    
    返回:
        np.ndarray: 符合要求的正态分布数组
    
    异常:
        ValueError: 参数不符合要求时抛出
    """
    # 参数验证
    if not isinstance(size, int) or size <= 0:
        raise ValueError("数量必须为正整数")
    if std_dev < 0:
        raise ValueError("标准差必须为非负数")
    if size == 1 and std_dev != 0:
        raise ValueError("当数量为1时，标准差必须为0")
    
    # 处理特殊情况（size=1）
    if size == 1:
        return np.array([mean])
    
    # 生成标准正态分布数据
    data = np.random.randn(size)
    
    # 标准化并调整到目标参数
    data = (data - np.mean(data)) / np.std(data)  # 标准化到均值0，标准差1
    data = data * std_dev + mean                   # 调整到目标参数
    
    #data = np.round(data, 2) ## s数组保留2位小数（四舍五入）
    return data

# 定义布局
progress_bar_max = 100
BAR_COLORS = ('#2f89fc', sg.theme_background_color())  # (前景色, 背景色)

layout = [
    [sg.Text("拉伸/压缩载荷下含体积型缺陷管体可靠性评价(TSC/CSC)", 
             justification='center', size=(90, 1), background_color='#2f89fc', font=('Arial', 15, 'bold'),
             text_color="white", pad=(0, 0))],
    
    [sg.Frame(
        "填写信息",
        [
            [sg.Text("填表人：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Input(key="-NAME-", expand_x=True, pad=(10, 10))],
            [sg.Text("填写日期：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Input(key="-DATE-", default_text=formatted_date, expand_x=True, pad=(10, 10))],
            [sg.Text("备注信息：", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Multiline(key="-REMARK-", size=(10, 5), expand_x=True, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    )],
    
    [sg.Frame(
        "管道几何结构",
        [
            [sg.Text("管道外径(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 1422", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("管道壁厚(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 32.1", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    ),
    sg.Frame(
        "运行压力",
        [
            [sg.Text("内压", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 9.16", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0.916", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    )],
    
    [sg.Frame(
        "管道力学性能",
        [
            [sg.Text("屈强比", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 0.901", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0", size=(11, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("屈服强度(MPa)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 548", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("抗拉强度(MPa)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 608", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    ),
    sg.Frame(
        "缺陷几何",
        [
            [sg.Text("缺陷环向长度(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 32", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("缺陷轴向长度(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 275", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 5.5", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))],
            [sg.Text("缺陷深度(mm)", size=(15, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("均值: 10", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10)), sg.Text("标准差: 0", size=(10, 1), text_color="black", background_color="white", border_width=0, pad=(10, 10))]
        ],
        title_color="black",
        background_color="white",
        border_width=1,
        relief=sg.RELIEF_SUNKEN,
        expand_x=True,
        pad=(10, 10)
    )],
    
    [
        sg.Column([
            [sg.Text("应变需求", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(default_text="0.5", key="-RELIABILITY 0-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white'),
        sg.Column([
            [sg.Text("目标可靠度", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(default_text="0.00035", key="-ADAPTABILITY-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white')
    ],
    
    # 第二行：失效概率 + 可靠度评价（并排）
    [
        sg.Column([
            [sg.Text("失效概率", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(key="-FAILURE-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white'),
        sg.Column([
            [sg.Text("可靠度评价", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(key="-RELIABILITY-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white')
    ],
    
    # 第三行：抽样数量 + 操作按钮（并排）
    [
        sg.Column([
            [sg.Text("抽样数量", size=(15,1), text_color="black", background_color="white", border_width=0, pad=(10,10)),
             sg.Input(default_text="10", key="-SAMPLE-", size=(20,1), text_color="black", background_color="white", pad=(10,10))]
        ],background_color='white'),
        sg.Column([
            
            [sg.Combo(['TSC','CSC'], key='-COMBO-', pad=(10,10),text_color='white',
                      background_color='#2f89fc',size=(10,1),default_value='请选择...',
                      ),
             sg.Button("计算", pad=(10,10), button_color=('white','#2f89fc'),size=(12,1)),
             sg.Button("导出到Sheet", pad=(10,10), button_color=('white','#2f89fc'),size=(12,1))]
        ],background_color='white')
    ],
    
    [sg.ProgressBar(progress_bar_max, orientation='h', size=(30.5, 20), key='-PROGRESS-', bar_color=BAR_COLORS),sg.Text('0%', size=(5, 1), key='-PERCENT-')]
    
]

# 创建窗口
window = sg.Window("拉伸/压缩载荷下含体积型缺陷管体可靠性评价(TSC/CSC)", layout, background_color="white", resizable=True)

# 启动事件循环
while True:
    
    event, values = window.read()
    if event == sg.WINDOW_CLOSED:
        break
    elif event == "计算":
        
        sample = int(values['-SAMPLE-'])
        input_name = values['-NAME-']
        input_date = values['-DATE-']
        input_remark = values['-REMARK-']
        
        rel = float(values['-RELIABILITY 0-'])
        ada = float(values['-ADAPTABILITY-'])
        cal = values['-COMBO-']
        #print(cal)
        if cal == '请选择...':
            cal = 'TSC'  #未选择 默认使用TSC计算
            
        elif cal == 'CSC':
            file_path = 'database_CSC.xlsx'
            data = pd.read_excel(file_path,header=0)
            data.columns = ['Wc','Lc','dc','TSC']
        
        if int(sample)<=0:
            sample = 10
        D_sample = generate_normal_array(mean=1422, std_dev=0, size=sample)#管径
        P_sample = generate_normal_array(mean=9, std_dev=0, size=sample)#内压
        Lc_sample = generate_normal_array(mean=196, std_dev=0, size=sample) #长度
        d_sample = generate_normal_array(mean=13, std_dev=0, size=sample) #深度
        t_sample = generate_normal_array(mean=32.1, std_dev=0, size=sample)#壁厚
        w_sample = generate_normal_array(mean=320, std_dev=12, size=sample)#宽度
        thetay_sample = generate_normal_array(mean=548, std_dev=0, size=sample)#屈服
        thetaU_sample = generate_normal_array(mean=596, std_dev=0, size=sample)#抗拉
        ryt_sample = thetay_sample/thetaU_sample
        
        # 核心计算（带分母非零验证）
        denominator_sample = (thetay_sample * 2 * t_sample) / D_sample
        if np.any(denominator_sample == 0):
            raise ZeroDivisionError("分母存在零值，请检查输入数据")
            
        fp_sample = P_sample / denominator_sample
        
        TSC = []
        TSC_labels = []
        progress = 0
        gap =  progress_bar_max/sample
        start_time = time.time()
        for i in range(sample):
            #TSC_i = calculate(Wc, Lc, d, D, t)
            TSC_i = calculate(w_sample[i], Lc_sample[i], d_sample[i], D_sample[i], t_sample[i], ryt_sample[i], fp_sample[i], cal)
            TSC.append(TSC_i)
            label = 1 if TSC_i > rel else 0
            TSC_labels.append(label)
            # 模拟进度更新
            if progress < progress_bar_max:
                progress += gap
                window['-PROGRESS-'].update(progress)  # 更新进度条
                percent = int((progress / progress_bar_max) * 100)
                window['-PERCENT-'].update(f'{percent}%')  # 更新文本内容
            else:
                window['-PROGRESS-'].update(progress_bar_max)  # 确保进度条填满
                window['-PERCENT-'].update(f'{100}%')  # 更新文本内容
                
        
        failure = sum(TSC_labels) / sample  # 假设失效概率为0.8

        if failure > ada:
            reliability = "不安全" 
        elif failure < ada:
            reliability = "安全" 
        else:
            reliability = "临界" 
            
        failure = round(failure, 5)  # 假设失效概率为0.8
        
        # 更新界面
        window['-FAILURE-'].update(failure)
        window['-RELIABILITY-'].update(reliability)
        
        end_time = time.time()
        output = zip(D_sample, t_sample, thetay_sample, thetaU_sample, ryt_sample, P_sample, fp_sample, 
                     Lc_sample, w_sample, d_sample, TSC,
                    [input_name] * sample,[input_date] * sample,[input_remark] * sample)
        sg.popup("计算完成", f"抽样数量{sample}\n计算耗时：{(end_time - start_time)/60:.2f}分钟")
        
    elif event == "导出到Sheet":
        try:
            cal = values['-COMBO-']
            #print(cal)
            if cal == '请选择...':
                cal = 'TSC'  #未选择 默认使用TSC计算
            # 下载或者创建一个新的工作簿和工作表
            myname = f'随机抽样管道数据_{cal}'
            file_name = f"{myname}.xlsx"
            wb = load_workbook(file_name) if os.path.exists(file_name) else Workbook()
            wb.save(f"{myname}_bak.xlsx")
            ws = wb.active
            if myname not in wb.sheetnames:
                #print('')
                ws = wb.create_sheet(f"{myname}")
                headers = [
                    ["编号", "管道几何尺寸","", "材料力学性能参数", "", "", "运行压力", "压力因子", "体积型缺陷尺寸", "", "", cal,"填表人","填表日期", "备注"], #cal TSC CSC
                    ["", "管道外直径","壁厚", "管道屈服强度", "管道抗拉强度", "屈强比", "", "", "缺陷长度", "缺陷宽度", "缺陷深度", "", "", "", ""],
                    ["", "mm", "mm","MPa", "MPa", "MPa/MPa", "MPa", "MPa/MPa", "mm", "mm", "mm", "%","","",""]
                ]
                for row in headers:
                    ws.append(row)

                # 合并单元格（例如，合并A1到C1）
                merge_list = ['A1:A3','B1:C1','D1:F1','G1:G2','H1:H2','I1:K1','L1:L2','M1:M3','N1:N3','O1:O3']
                for merge_cells in merge_list:
                    ws.merge_cells(merge_cells)
            else:
                ws = wb[f"{myname}"]
            row_count = len(ws["A"]) - 2
            new_color_cell = [i+str(j) for j in range(row_count,row_count+sample+3) for i in ['B','C','G','H','L','N']]
            for row_out in output:
                ws.append([row_count] + list(row_out))
                row_count += 1

            color_cell = ['B1','B2','B3','C1','C2','C3',
                          'G1','G3','H1','H3','L1','L3','N1'] #合并后给左上第一个单元格涂色就是给合并后整体涂色

            color_cell = color_cell + new_color_cell
            # 设置所有单元格的字体为居中加粗
            for row in ws.iter_rows():
                for cell in row:
                    cell.alignment = Alignment(horizontal='center', vertical='center')
                    cell.font = Font(bold=True)
                    # 设置实线边框
                    cell.border = Border(left=Side(style='thin'),
                                         right=Side(style='thin'),
                                         top=Side(style='thin'),
                                         bottom=Side(style='thin'))
                    #设置背景色为浅绿色(如果是合并后的单元格，使用左上角单元格编号)
                    if cell.coordinate in color_cell:
                        cell.fill = PatternFill(start_color="F0FFF0", end_color="F0FFF0", fill_type="solid")

            # 保存工作簿
            wb.save(file_name)
        except Exception as e:
            sg.popup("导出失败", f"{str(e)}")
            raise SystemExit(f"错误: {str(e)}")
window.close()