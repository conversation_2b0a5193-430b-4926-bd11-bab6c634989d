#!/usr/bin/env python
# coding: utf-8

# In[1]:


import os
import numpy as np
import PySimpleGUI as sg
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Alignment, Font, PatternFill,Border, Side

def cal_tsc_crack(c, a, t, fp, thetaA, thetay, thetaU):
    """
    
    """
    c = float(c)
    a = float(a)
    t = float(t)
    thetaA = float(thetaA)
    thetay = float(thetay)
    thetaU = float(thetaU)
    # a列
    a1 = 2.084E+00
    a2 = 2.812E-01
    a3 = -4.950E-01
    a4 = 7.373E-01
    a5 = -5.005E+00
    a6 = 1.186E+00
    a7 = 1.644E+00
    a8 = 7.374E-01
    a9 = -9.829E-01
    a10 = 8.655E-02
    a11 = -1.029E-01
    a12 = -1.500E-01
    a13 = 1.025E+00
    a14 = 5.557E+00

    # b列
    b1 = -5.005E-02
    b2 = -5.139E-03
    b3 = 4.485E-01
    b4 = 1.417E+00
    b5 = 2.217E+00
    b6 = 1.029E+00
    b7 = -2.598E+00
    b8 = -2.679E+00
    b9 = 1.694E+00

    # c列
    c1 = 1.409E+00
    c2 = 2.345E-01
    c3 = 1.125E+00
    c4 = 4.181E+00
    c5 = 1.201E+00
    c6 = -5.384E+00
    c7 = 2.406E+00
    c8 = -2.154E-01
    c9 = -5.237E-03
    c10 = 9.889E+00
    c11 = 3.547E-01
    c12 = -7.513E-01

    # d列
    d1 = 2.209E-02
    d2 = 1.156E+00
    d3 = 1.601E+00
    d4 = 8.964E-01
    d5 = 1.383E+00
    d6 = 1.333E+00
    d7 = 9.313E-02
    d8 = -2.240E+00
    d9 = 8.559E-00
    d10 = -3.719E+00

    #fp = 0.7
    #TSCp = 0.9
    
    #thetaA = 0.01
    e = 2.71 #自然常数
    h = 1
    phi = 1

    ########### 以上是常量

    beta, eta, psi, xi = 2*c/t, a/t, h/t, thetay/thetaU

    # Define the equations
    A = (a1 * (e ** (a2 / beta)) * (e ** (a3 * eta * beta * (e ** (a4 / beta)))) *
         (1 + a5 * (psi ** a6) + a7 * psi * (eta * beta) ** a8) *
         (1 + a9 * (xi ** a10) * (phi ** a11) + a12 * (psi ** a13) * (xi ** a14)))

    B = (beta ** b1 * (eta ** (b2 * (beta ** b3) / eta)) *
         (b4 * (phi ** b5) * ((b6 * (phi ** b7)) ** xi) + b8 * (psi ** b9)))

    C = (e ** (c1 / beta) * (e ** ((c2 * beta) / ((1 + c3 * beta) * eta))) *
         (1 + c4 * (psi ** c5) + c6 * psi * (eta ** -eta) + c7 * psi * (eta ** -beta)) *
         (c8 + c9 * (phi ** 10) + c11 * (xi ** c12) * phi))

    D = (d1 * (beta ** d2) * (eta ** ((d3 * beta) / (1 + d4 * beta))) *
         (1 + d5 * (psi ** d6)) * (1 + d7 * (xi ** d8) + d9 * (phi ** 10)))

    TSCp = A * (C*thetaA) ** (B*(thetaA**D))
    
    if 0.6<=fp<=0.8:
        return TSCp
    else:
        TSC0 = 1.5 * TSCp
        return TSC0 + (5 * fp/3) * (TSCp - TSC0)
    return TSCp

def generate_normal_array(mean: float, std_dev: float, size: int) -> np.ndarray:
    """
    生成符合指定样本均值和标准差的正态分布数组
    
    参数:
        mean (float): 目标样本均值
        std_dev (float): 目标样本标准差（非负数）
        size (int): 输出数组长度（必须为正整数）
    
    返回:
        np.ndarray: 符合要求的正态分布数组
    
    异常:
        ValueError: 参数不符合要求时抛出
    """
    # 参数验证
    if not isinstance(size, int) or size <= 0:
        raise ValueError("数量必须为正整数")
    if std_dev < 0:
        raise ValueError("标准差必须为非负数")
    if size == 1 and std_dev != 0:
        raise ValueError("当数量为1时，标准差必须为0")
    
    # 处理特殊情况（size=1）
    if size == 1:
        return np.array([mean])
    
    # 生成标准正态分布数据
    data = np.random.randn(size)
    
    # 标准化并调整到目标参数
    data = (data - np.mean(data)) / np.std(data)  # 标准化到均值0，标准差1
    data = data * std_dev + mean                   # 调整到目标参数
    
    return data

def validate_parameters(*args):
    for inx, param in enumerate(args):
        if inx == 19 and param not in ['TSC','CSC']:
            return 201, f"'{desc[inx]}' must be TSC or CSC"
        else:
            return 200, 'success'
            
        if not isinstance(param, (int, float)):
            return 202, f"'{desc[inx]}' must be a number"
        
        if param <= 0:
            return 203, f"'{desc[inx]}' must be non-negative"
        
    return 200, 'success'

param = {
    'Sample' : 10, #抽样数量
    'Reliability':0.5,#失效概率基值
    'Adaptability':0.00035,#可靠度评价基值
    'EnterInternalPressure_Mean': 9.16,  # 输入内压均值
    'EnterInternalPressure_Standard_Deviation': 0.916,  # 输入内压标准差
    'PipeYieldStrength_Mean': 528,  # 管道屈服强度均值
    'PipeYieldStrength_Standard_Deviation': 21.12,  # 管道屈服强度标准差
    'PipeTensileStrength_Mean': 625,  # 管道抗拉强度均值
    'PipeTensileStrength_Standard_Deviation': 12.5,  # 管道抗拉强度标准差
    'OuterDiameter_Mean': 1422,  # 管道外径均值
    'OuterDiameter_Standard_Deviation': 42.66,  # 管道外径标准差
    'WallThickness_Mean': 32.1,  # 管道壁厚均值
    'WallThickness_Standard_Deviation': 1.605,  # 管道壁厚标准差
    'DefectLength_Mean': 30,  # 裂纹长度均值
    'DefectLength_Standard_Deviation': 0.9,  # 裂纹长度标准差
    'DefectDepth_Mean': 6,  # 裂纹深度均值
    'DefectDepth_Standard_Deviation': 0.6,  # 裂纹深度标准差
    'CTOD_Mean':0.18, #thetaA 均值
    'CTOD_Std':0.01, #thetaA 标准差
    'YieldStrengthRatio_Mean': 0.902,  # 屈强比均值
    'YieldStrengthRatio_Standard_Deviation': 0.0916,  # 屈强比标准差
    'CalOut': 'TSCcrack',  # 计算输出标识，具体含义依场景而定
}

def main(param):
    # 获取输入值
    
    sample =param.get('Sample') or 10 #默认计算TSC
    rel = param.get('Reliability') or 0.5
    ada = param.get('Adaptability') or 0.00035
    cal = param.get('CalOut') or 'TSCcrack' #默认计算TSCcrack
    EnterInternalPressure_Mean = param.get('EnterInternalPressure_Mean') or 9.16
    EnterInternalPressure_Std = param.get('EnterInternalPressure_Standard_Deviation') or 0.916
    PipeYieldStrength_Mean = param.get('PipeYieldStrength_Mean') or 528
    PipeYieldStrength_Std = param.get('PipeYieldStrength_Standard_Deviation') or 21.12
    PipeTensileStrength_Mean = param.get('PipeTensileStrength_Mean') or 625
    PipeTensileStrength_Std = param.get('PipeTensileStrength_Standard_Deviation') or 12.5
    OuterDiameter_Mean = param.get('OuterDiameter_Mean') or 1422
    OuterDiameter_Std = param.get('OuterDiameter_Standard_Deviation') or 42.66
    WallThickness_Mean = param.get('WallThickness_Mean') or 32.1
    WallThickness_Std = param.get('WallThickness_Standard_Deviation') or 1.605
    DefectLength_Mean = param.get('DefectLength_Mean') or 30
    DefectLength_Std = param.get('DefectLength_Standard_Deviation') or 0.9
    DefectDepth_Mean = param.get('DefectDepth_Mean') or 6
    DefectDepth_Std = param.get('DefectDepth_Standard_Deviation') or 0.6
    CTOD_Mean = param.get('CTOD_Mean') or 0.18
    CTOD_Std = param.get('CTOD__Std') or 0.01
    YieldStrengthRatio_Mean = param.get('YieldStrengthRatio_Mean') or 0.902
    YieldStrengthRatio_Std = param.get('YieldStrengthRatio_Standard_Deviation') or 0.0916

#     myargs = [D, t, P, Y, T, cal]
#     desc = ['OuterDiameter', 'WallThickness', 'EnterInternalPressure', 
#             'PipeYieldStrength', 'PipeTensileStrength','CalOut']

    myargs = [EnterInternalPressure_Mean, EnterInternalPressure_Std, PipeYieldStrength_Mean, PipeYieldStrength_Std,
              PipeTensileStrength_Mean, PipeTensileStrength_Std, OuterDiameter_Mean, OuterDiameter_Std,
              WallThickness_Mean, WallThickness_Std, DefectLength_Mean, DefectLength_Std, DefectDepth_Mean,
              DefectDepth_Std, CTOD_Mean,CTOD_Std,
              YieldStrengthRatio_Mean, YieldStrengthRatio_Std, cal]
    
    
    desc = ["EnterInternalPressure_Mean", "EnterInternalPressure_Standard_Deviation", "PipeYieldStrength_Mean",
            "PipeYieldStrength_Standard_Deviation", "PipeTensileStrength_Mean", "PipeTensileStrength_Standard_Deviation",
            "OuterDiameter_Mean", "OuterDiameter_Standard_Deviation", "WallThickness_Mean", "WallThickness_Standard_Deviation",
            "DefectLength_Mean", "DefectLength_Standard_Deviation", "DefectDepth_Mean", "DefectDepth_Standard_Deviation",
            "CTOD_Mean","CTOD_Std",
             "YieldStrengthRatio_Mean", "YieldStrengthRatio_Standard_Deviation", "CalOut"]


    results = {'code':200,
              'data':{},
              'param':param,
              'message':'success'}
    
    code, mes = validate_parameters(*myargs)
    
    if code == 200:
        # 生成正态分布数组，使用之前定义的变量作为均值和标准差
        D_sample = generate_normal_array(mean=OuterDiameter_Mean, std_dev=OuterDiameter_Std, size=sample)
        P_sample = generate_normal_array(mean=EnterInternalPressure_Mean, std_dev=EnterInternalPressure_Std, size=sample)
        Lc_sample = generate_normal_array(mean=DefectLength_Mean, std_dev=DefectLength_Std, size=sample)  # 长度
        d_sample = generate_normal_array(mean=DefectDepth_Mean, std_dev=DefectDepth_Std, size=sample)  # 深度
        t_sample = generate_normal_array(mean=WallThickness_Mean, std_dev=WallThickness_Std, size=sample)
        #w_sample = generate_normal_array(mean=DefectWidth_Mean, std_dev=DefectWidth_Std, size=sample) 
        thetay_sample = generate_normal_array(mean=PipeYieldStrength_Mean, std_dev=PipeYieldStrength_Std, size=sample)
        thetaU_sample = generate_normal_array(mean=PipeTensileStrength_Mean, std_dev=PipeTensileStrength_Std, size=sample)
        CTOD_sample = generate_normal_array(mean=CTOD_Mean, std_dev=CTOD_Std, size=sample)

        #计算的压力因子
        ryt_sample = thetay_sample/thetaU_sample
        
        # 核心计算（带分母非零验证）
        denominator_sample = (thetay_sample * 2 * t_sample) / D_sample
        if np.any(denominator_sample == 0):
            raise ZeroDivisionError("分母存在零值，请检查输入数据")
            
        fp_sample = P_sample / denominator_sample
        
        TSC = []
        TSC_labels = []
        for i in range(sample):
            #TSC_i = calculate(w_sample[i], Lc_sample[i], d_sample[i], D_sample[i], t_sample[i], ryt_sample[i], cal)
            TSC_i = cal_tsc_crack(Lc_sample[i], d_sample[i], t_sample[i], fp_sample[i], CTOD_sample[i], thetay_sample[i], thetaU_sample[i])
            #TSC_i = cal_tsc_crack(c, a, t, fp, thetaA, thetay, thetaU)
            TSC.append(TSC_i)
            label = 1 if TSC_i > rel else 0
            TSC_labels.append(label)
            
        failure = sum(TSC_labels) / sample  # 假设失效概率为0.8

        if failure > ada:
            reliability = "不安全" 
        elif failure < ada:
            reliability = "安全" 
        else:
            reliability = "临界"
            
        failure = round(failure, 5)  # 假设失效概率为0.8
            
        results['data'] = {'failure':failure,'reliability':reliability}
    else:
        results['code'] = code
        results['message'] = mes
    return results

if '__name__' == '__mian__':
    results = main(param)

